from langchain_core.messages import BaseMessage
from typing import Sequence, List, Dict, Any
from langchain.prompts import Chat<PERSON><PERSON><PERSON><PERSON>emplate

# Import or define init_chat_model
from langchain.chat_models import init_chat_model

def remove_tool_calls(
        messages: Sequence[BaseMessage],
        save_last_message_tool_call: int = 0 # Number of last responses when tool called to save the tool calls in. 0 means delete all as soon as possible. Negative value means save all tool calls (default: 0)
        ) -> Sequence[BaseMessage]:
    
    if messages[-1].type == "tool" or save_last_message_tool_call < 0:
        return messages

    tool_call_removed_messages = []

    # keep_processing = True
    response_with_tool_call_num = 0
    response_temp_index=0
    last_tool_called_index=-1

    for message in reversed(messages):

        tool_calls = []
        if hasattr(message, 'tool_calls'):
            tool_calls = message.tool_calls

        if response_temp_index > 0  and last_tool_called_index != response_temp_index and message.type == "tool":
            response_with_tool_call_num += 1
            last_tool_called_index = response_temp_index


        if not ( response_with_tool_call_num > save_last_message_tool_call and (message.type == "tool" or tool_calls != []) ) :
            tool_call_removed_messages.insert(0, message)

        if message.type == "human":
            response_temp_index += 1

    return list(tool_call_removed_messages)
