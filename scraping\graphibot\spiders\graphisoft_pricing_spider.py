import scrapy
import re

class GraphisoftPricingSpider(scrapy.Spider):
    name = "pricingbot"
    buy_now_url = "https://www.graphisoft.com/buy-now"
    ajax_url = "https://www.graphisoft.com/cms/wp-admin/admin-ajax.php" # As extracted from GS.ajaxurl from buy-now page on 2025-06-16
    custom_settings = {
        "ITEM_PIPELINES": {
            # "graphibot.pipelines.MongoDBPipeline": 100,
            "graphibot.pipelines.PostgreSQLPipeline": 100,
        },
    }

    def start_requests(self):
        for idx, code in enumerate(self.countries.keys()):
            yield scrapy.FormRequest(
                url = self.ajax_url,
                method ="POST",
                formdata = {
                    "action": "select-country",
                    "code": code,
                    "region": "",
                },
                callback = self.handle_country_redirect,
                cb_kwargs= {"code": code},
                dont_filter = True,
                meta = {"cookiejar": idx},
            )

    def handle_country_redirect(self, response, code):
        yield scrapy.Request(
            url = self.buy_now_url,
            callback = self.parse_buy_now,
            cb_kwargs= {"code": code},
            dont_filter = True,
            meta = {"cookiejar": response.meta["cookiejar"]},
        )
    
    def parse_buy_now(self, response, code):
        self.logger.info(f"Scraping buy now | country code: {code}")
        
        # Extract terms (columns of licencing table)
        term_headers = []
        for header in response.css("table.licensing-table thead tr.licensing-table-terms th.licensing-header"):
            term = header.css("p:first-child::text").get().strip()
            term_headers.append(term)

        # Extract subscriptions (rows of licencing table)
        subscriptions = []
        for row in response.css("table.licensing-table tbody tr"):
            subscription = row.css("td a.licensing-table-links::text").get().strip()

            terms = {}
            for idx, term in enumerate(term_headers, start = 2): # Start from 2 to skip the first column (subscription name)
                price_span = row.css(f"td:nth-child({idx}) span:first-child::text").get()
                price_per_month_str = price_span.strip() if price_span else None

                # Extract currency and price value using the dedicated function
                price_value, currency = self.extract_price_and_currency(price_per_month_str)

                discount = row.css(f"td:nth-child({idx}) .price-discount::text").get()
                discount = discount.strip() + "%" if discount else None

                pay_now = row.css(f"td:nth-child({idx}) .licensing-pay-text:contains('Pay now')::text").get()
                pay_now = pay_now.replace("Pay now: ", "").strip() if pay_now else None

                total_price = row.css(f"td:nth-child({idx}) .licensing-pay-text:contains('Total price')::text").get()
                total_price = total_price.replace("Total price: ", "").strip() if total_price else None

                terms[term] = {
                    "price_per_month_str": price_per_month_str,  # Original string
                    "price_value": price_value,                  # Numeric value
                    "currency": currency,                        # Currency code
                    "discount": discount,
                    "pay_now": pay_now,
                    "total_price": total_price,
                }
            
            subscription = {
                "subscription": subscription,
                "terms": terms,
            }

            subscriptions.append(subscription)

        yield {
            "country_code": code,
            "country_name": self.countries[code],
            "subscriptions": subscriptions,
        }

    def extract_price_and_currency(self, price_str):
        """
        Extract numeric price value and currency from a price string.
        
        Args:
            price_str (str): The price string to parse (e.g., "HUF 87,000")
            
        Returns:
            tuple: (price_value, currency) where price_value is a float and currency is a string
        """
        price_value = None
        currency = None
        
        if not price_str:
            return price_value, currency
            
        # Extract the price value
        numeric_pattern = r'[\d,.]+'
        numeric_matches = re.findall(numeric_pattern, price_str)
        if numeric_matches:
            # Convert to float, removing commas
            price_value = float(numeric_matches[0].replace(',', ''))
        
        # Extract currency using common patterns
        currency_patterns = {
            r'HUF': 'HUF',
            r'MXN':'MXN',
            r'r$': 'BRL',
            r'CA$': 'CAD',
            r'HK$': 'HKD',
            r'NZ$': 'NZD',
            r'A$': 'AUD',
            r'\$': 'USD',
            r'€': 'EUR',
            r'£': 'GBP',
            r'¥': 'JPY'
        }
        
        currency_found = False
        for pattern, curr in currency_patterns.items():
            if re.search(pattern, price_str):
                currency = curr
                currency_found = True
                break
        
        # If no common currency pattern matched, extract non-numeric part
        if not currency_found:
            # Remove digits, commas, dots, spaces, plus signs, and "tax", "seat" or "/"
            non_numeric = re.sub(r'[\d,.\s+]|tax|seat|/', '', price_str).strip()
            if non_numeric:
                currency = non_numeric[:3].upper()
                
        return price_value, currency


    # Manually maintained list of regions as extracted from the countries global variable
    # From https://graphisoft.com/buy-now with console.log(countries) as of 2025-06-16
    countries = {
        "AF": "Afghanistan",
        "AX": "Aland Islands",
        "AL": "Albania",
        "DZ": "Algeria",
        "AD": "Andorra",
        "AO": "Angola",
        "AI": "Anguilla",
        "AQ": "Antarctica",
        "AG": "Antigua and Barbuda",
        "AR": "Argentina",
        "AM": "Armenia",
        "AW": "Aruba",
        "AU": "Australia",
        "AT": "Austria",
        "AZ": "Azerbaijan",
        "BS": "Bahamas",
        "BH": "Bahrain",
        "BD": "Bangladesh",
        "BB": "Barbados",
        "BY": "Belarus",
        "BE": "Belgium",
        "BZ": "Belize",
        "BJ": "Benin",
        "BM": "Bermuda",
        "BT": "Bhutan",
        "BO": "Bolivia, Plurinational State of",
        "BQ": "Bonaire, Sint Eustatius and Saba",
        "BA": "Bosnia and Herzegovina",
        "BW": "Botswana",
        "BV": "Bouvet Island",
        "BR": "Brazil",
        "IO": "British Indian Ocean Territory",
        "BN": "Brunei Darussalam",
        "BG": "Bulgaria",
        "BF": "Burkina Faso",
        "BI": "Burundi",
        "KH": "Cambodia",
        "CM": "Cameroon",
        "CA": "Canada",
        "CV": "Cape Verde",
        "KY": "Cayman Islands",
        "CF": "Central African Republic",
        "TD": "Chad",
        "CL": "Chile",
        "CN": "China",
        "CX": "Christmas Island",
        "CC": "Cocos (Keeling) Islands",
        "CO": "Colombia",
        "KM": "Comoros",
        "CG": "Congo",
        "CD": "Congo, the Democratic Republic of the",
        "CK": "Cook Islands",
        "CR": "Costa Rica",
        "CI": "Cote d'Ivoire",
        "HR": "Croatia",
        "CU": "Cuba",
        "CW": "Curaçao",
        "CY": "Cyprus",
        "CZ": "Czech Republic",
        "DK": "Denmark",
        "DJ": "Djibouti",
        "DM": "Dominica",
        "DO": "Dominican Republic",
        "EC": "Ecuador",
        "EG": "Egypt",
        "SV": "El Salvador",
        "GQ": "Equatorial Guinea",
        "ER": "Eritrea",
        "EE": "Estonia",
        "ET": "Ethiopia",
        "FK": "Falkland Islands (Malvinas)",
        "FO": "Faroe Islands",
        "FJ": "Fiji",
        "FI": "Finland",
        "FR": "France",
        "GF": "French Guiana",
        "PF": "French Polynesia",
        "TF": "French Southern Territories",
        "GA": "Gabon",
        "GM": "Gambia",
        "GE": "Georgia",
        "DE": "Germany",
        "GH": "Ghana",
        "GI": "Gibraltar",
        "GR": "Greece",
        "GL": "Greenland",
        "GD": "Grenada",
        "GP": "Guadeloupe",
        "GT": "Guatemala",
        "GG": "Guernsey",
        "GN": "Guinea",
        "GW": "Guinea-Bissau",
        "GY": "Guyana",
        "HT": "Haiti",
        "HM": "Heard Island and McDonald Islands",
        "VA": "Holy See (Vatican City State)",
        "HN": "Honduras",
        "HK": "Hong Kong",
        "HU": "Hungary",
        "IS": "Iceland",
        "IN": "India",
        "ID": "Indonesia",
        "IR": "Iran, Islamic Republic of",
        "IQ": "Iraq",
        "IE": "Ireland",
        "IM": "Isle of Man",
        "IL": "Israel",
        "IT": "Italy",
        "JM": "Jamaica",
        "JP": "Japan",
        "JE": "Jersey",
        "JO": "Jordan",
        "KZ": "Kazakhstan",
        "KE": "Kenya",
        "KI": "Kiribati",
        "KP": "Korea, Democratic People's Republic of",
        "KR": "Korea, Republic of",
        "XK": "Kosovo",
        "KW": "Kuwait",
        "KG": "Kyrgyzstan",
        "LA": "Lao People's Democratic Republic",
        "LV": "Latvia",
        "LB": "Lebanon",
        "LS": "Lesotho",
        "LR": "Liberia",
        "LY": "Libya",
        "LI": "Liechtenstein",
        "LT": "Lithuania",
        "LU": "Luxembourg",
        "MO": "Macao",
        "MK": "Macedonia, the former Yugoslav Republic of",
        "MG": "Madagascar",
        "MW": "Malawi",
        "MY": "Malaysia",
        "MV": "Maldives",
        "ML": "Mali",
        "MT": "Malta",
        "MQ": "Martinique",
        "MR": "Mauritania",
        "MU": "Mauritius",
        "YT": "Mayotte",
        "MX": "Mexico",
        "MD": "Moldova, Republic of",
        "MC": "Monaco",
        "MN": "Mongolia",
        "ME": "Montenegro",
        "MS": "Montserrat",
        "MA": "Morocco",
        "MZ": "Mozambique",
        "MM": "Myanmar",
        "NA": "Namibia",
        "NR": "Nauru",
        "NP": "Nepal",
        "NL": "Netherlands",
        "NC": "New Caledonia",
        "NZ": "New Zealand",
        "NI": "Nicaragua",
        "NE": "Niger",
        "NG": "Nigeria",
        "NU": "Niue",
        "NF": "Norfolk Island",
        "NO": "Norway",
        "OM": "Oman",
        "PK": "Pakistan",
        "PS": "Palestine",
        "PA": "Panama",
        "PG": "Papua New Guinea",
        "PY": "Paraguay",
        "PE": "Peru",
        "PH": "Philippines",
        "PN": "Pitcairn",
        "PL": "Poland",
        "PT": "Portugal",
        "PR": "Puerto Rico",
        "QA": "Qatar",
        "RE": "Reunion",
        "RO": "Romania",
        "RU": "Russian Federation",
        "RW": "Rwanda",
        "BL": "Saint Barthélemy",
        "SH": "Saint Helena, Ascension and Tristan da Cunha",
        "KN": "Saint Kitts and Nevis",
        "LC": "Saint Lucia",
        "MF": "Saint Martin (French part)",
        "PM": "Saint Pierre and Miquelon",
        "VC": "Saint Vincent and the Grenadines",
        "WS": "Samoa",
        "SM": "San Marino",
        "ST": "Sao Tome and Principe",
        "SA": "Saudi Arabia",
        "SN": "Senegal",
        "RS": "Serbia",
        "SC": "Seychelles",
        "SL": "Sierra Leone",
        "SG": "Singapore",
        "SX": "Sint Maarten (Dutch part)",
        "SK": "Slovakia",
        "SI": "Slovenia",
        "SB": "Solomon Islands",
        "SO": "Somalia",
        "ZA": "South Africa",
        "GS": "South Georgia and the South Sandwich Islands",
        "SS": "South Sudan",
        "ES": "Spain",
        "LK": "Sri Lanka",
        "SD": "Sudan",
        "SR": "Suriname",
        "SJ": "Svalbard and Jan Mayen",
        "SZ": "Swaziland",
        "SE": "Sweden",
        "CH": "Switzerland",
        "SY": "Syrian Arab Republic",
        "TW": "Taiwan",
        "TJ": "Tajikistan",
        "TZ": "Tanzania, United Republic of",
        "TH": "Thailand",
        "TL": "Timor-Leste",
        "TG": "Togo",
        "TK": "Tokelau",
        "TO": "Tonga",
        "TT": "Trinidad and Tobago",
        "TN": "Tunisia",
        "TR": "Turkey",
        "TM": "Turkmenistan",
        "TC": "Turks and Caicos Islands",
        "TV": "Tuvalu",
        "UG": "Uganda",
        "UA": "Ukraine",
        "AE": "United Arab Emirates",
        "GB": "United Kingdom",
        "US": "United States",
        "UY": "Uruguay",
        "UZ": "Uzbekistan",
        "VU": "Vanuatu",
        "VE": "Venezuela, Bolivarian Republic of",
        "VN": "Vietnam",
        "VG": "Virgin Islands, British",
        "WF": "Wallis and Futuna",
        "EH": "Western Sahara",
        "YE": "Yemen",
        "ZM": "Zambia",
        "ZW": "Zimbabwe"
    }
