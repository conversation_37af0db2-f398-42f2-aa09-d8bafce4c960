from langgraph.graph.message import add_messages

from agent.util.messages_tool_call_remover import remove_tool_calls

def add(left, right):
        """
        This function is used to add messages to the state.
        In this case, it also removes tool calls from the messages backwards.
        THIS IS A REALLY BAD PRACTICE, BEC<PERSON>USE IT HAVE A SIDE EFFECTS.
        IT WOULD BE BETTER TO USE A PRE-<PERSON><PERSON><PERSON> HOOK TO REMOVE TOOL CALLS.
        BUT WITH THE CURRENT AGENT-CHAT-UI AND LANGRAPH API IF I USE A PRE-MODEL HOOK THE CHAT HISTORY DISPLAY START TO DISPLAY MESSAGES TWICE, AND REDRAWING THE WHOLE CHAT_HISTORY FULLY WHEN UPDATE.

        IF YOU KNOW HOW TO FIX THIS IN CHAT UI, PLEASE LET ME KNOW.
        IF CHANGE CHAT UI CHANGE THIS FUNCTION AND MAKE A PRE-MODEL HOOK TO REMOVE TOOL CALLS.

        """

        added_messages = add_messages(left, right)
    
        added_messages = remove_tool_calls(added_messages, save_last_message_tool_call=3)

        return added_messages
